/*********************************************************************************************************************
* MSPM0G3507 Opensource Library 即（MSPM0G3507 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 MSPM0G3507 开源库的一部分
* 
* MSPM0G3507 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          zf_common_headfile
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          MDK 5.37
* 适用平台          MSPM0G3507
* 店铺链接          https://seekfree.taobao.com/
* 
* 修改记录
* 日期              作者                备注
* 2025-06-1        SeekFree            first version
********************************************************************************************************************/

#ifndef _zf_common_headfile_h_
#define _zf_common_headfile_h_

#include "stdio.h"
#include "stdint.h"
#include "string.h"

//===================================================芯片 SDK 底层===================================================
#include "ti_msp_dl_config.h"
//===================================================芯片 SDK 底层===================================================

//====================================================开源库公共层====================================================
#include "zf_common_typedef.h"
#include "zf_common_clock.h"
#include "zf_common_debug.h"
#include "zf_common_fifo.h"
#include "zf_common_font.h"
#include "zf_common_function.h"
#include "zf_common_interrupt.h"
//====================================================开源库公共层====================================================

//===================================================芯片外设驱动层===================================================
#include "zf_driver_adc.h"
#include "zf_driver_delay.h"
#include "zf_driver_exti.h"
#include "zf_driver_flash.h"
#include "zf_driver_gpio.h"
#include "zf_driver_pit.h"
#include "zf_driver_pwm.h"
#include "zf_driver_soft_iic.h"
#include "zf_driver_spi.h"
#include "zf_driver_timer.h"
#include "zf_driver_uart.h"
//===================================================芯片外设驱动层===================================================

//===================================================外接设备驱动层===================================================
#include "zf_device_absolute_encoder.h"
#include "zf_device_oled.h"
#include "zf_device_tft180.h"
#include "zf_device_ips114.h"
#include "zf_device_ips200.h"
#include "zf_device_ips200pro.h"
#include "zf_device_imu660ra.h"
#include "zf_device_imu963ra.h"
#include "zf_device_imu660rb.h"
#include "zf_device_type.h"
#include "zf_device_wifi_uart.h"
#include "zf_device_wifi_spi.h"
#include "zf_device_tsl1401.h"
#include "zf_device_dl1b.h"
#include "zf_device_dl1a.h"
#include "zf_device_wireless_uart.h"
#include "zf_device_key.h"

//===================================================外接设备驱动层===================================================

//===================================================应用组件层===================================================
#include "seekfree_assistant.h"
#include "seekfree_assistant_interface.h"
//===================================================应用组件层===================================================

//===================================================用户自定义文件===================================================
#define PI 3.1415926535898
#define RAD_TO_ANGLE(x)     ( (x) * 180.0 / PI )                                // 弧度转换为角度
#define FLASH_SECTION_INDEX       (0)                                 // 存储数据用的扇区
#define POINT_MAX  (40)
#define CIRCUIT_ONE   (A25)  //1路
#define CIRCUIT_TWO   (A24)  //2路
#define CIRCUIT_THREE  (B25) //3路
#define CIRCUIT_FOUR   (B24) //4路
#define LEFT_PWM  (PWM_TIM_A0_CH0_B8)
#define LEFT_GPIO  (B9)
#define RIGHT_PWM  (PWM_TIM_A0_CH1_B12)
#define RIGHT_GPIO  (B13)
#define KEY_1  (B1)
#define KEY_2   (B0)
#define KEY_3   (A31)
#define KEY_4   (A30)
#define LASER  (A14)
#define SERVO_270_MAX   1320
#define SERVO_270_MIN   175
#define SERVO_180_MAX   1200
#define SERVO_180_MIN   260
typedef struct{
    float acc_x;   //x轴加速度
    float acc_y;   //y轴加速度
    float acc_z;   //z轴加速度

    float gyro_x;  //x轴角速度
    float gyro_y;  //y轴角速度
    float gyro_z;  //z轴角速度
}IMU_param;

typedef struct
{
        float kp;
        float ki;
        float kd;
}IMU_pid_param;
//===================================================用户自定义文件===================================================
extern uint8 InitFlag;
extern float GyroOffset_Xdata,icm_data_acc_x,icm_data_gyro_x;
extern float GyroOffset_Ydata,icm_data_acc_y,icm_data_gyro_y;
extern float GyroOffset_Zdata,icm_data_acc_z,icm_data_gyro_z;
extern float yaw;
extern float Gyro_get_values_zPal;
//extern float eulerAngle_yaw,eulerAngle_pitch,eulerAngle_roll,eulerAngle_yaw_total,eulerAngle_yaw_old;
//extern float Q_info_q0,Q_info_q1,Q_info_q2,Q_info_q3;
//extern float param_Kp;
//extern float param_Ki;
extern uint8 time_count;
extern uint8 start_flag;
extern uint8 imu_menu_flag;
extern int16 expect_speed;
extern float kp;
extern float kd;
extern uint8 n1;
extern uint8 d1;
extern uint8 n;
extern uint8 pit_state ;
extern uint8 zhuangtaiji;
extern uint8 last_zhuangtaiji;
extern float expect_azimuth ;
extern uint8 change_count;
extern uint8 last_last_zhuangtaiji;
extern uint8 left_flag;
extern uint8 right_flag;
extern float position_error ;
extern float last_position_error;
extern float error_output;
extern int16 pwm_left;
extern int16 pwm_right;
extern int16 chasu;
extern int16 L_speed ;
extern int16 R_speed;
extern uint8 stop_flag;
extern int ready_to_count_turn;
extern uint16 counter;
extern uint8 change_flag;
extern float dt;
extern float q0,q1,q2,q3;
extern float pitch,roll,yaw_angle;
extern float pitch_last,roll_last,yaw_last;
extern float recipNorm;
extern float gravity_x,gravity_y,gravity_z;
extern float error_x,error_y,error_z;
extern float acc_error_x,acc_error_y,acc_error_z;
extern IMU_param filtered_data;
extern IMU_param imu_data;
extern uint8 circle_count;
extern uint8 total_count;
extern uint8 stop_count_flag;
extern uint16 mid_duty270;
extern uint16 mid_duty180;
extern uint8 n2;
extern uint8 d2;
extern uint16 up_counter;
extern uint8 up_start_flag;
extern uint16 level_counter;
extern uint8 level_start_flag;
extern uint8 up_stop_flag;
extern uint8 level_stop_flag;
extern uint16 servo_180_output;
extern uint16 servo_270_output;
extern uint8 flip_up_flag;
extern uint8 flip_level_flag;
extern uint8 up_increase_step;
extern uint8 level_increase_step;
extern uint8 catch_flag;
extern uint8 servo_menu_flag;
extern uint8 n3;
extern uint8 d3; 
extern uint8 aim_menu_flag;
extern uint8 first_road_judge_call;

void motor_output();
void  Gyro_Zero_drift_init() ;
void Gyro_get_rad_values();
void gyro_Zangle_get();
//void ICM_AHRSupdate(float gx, float gy, float gz, float ax, float ay, float az);
//void ICM_getEulerianAngles(void);
void pid_adjustment();
void imu_main();
void imu_arrows_move();
void menu_main();
void menu_main_show();
void arrows_move();
void pit_handler (uint32 state, void *ptr);
void road_judge();
int16 limit_ab(int16 x,int16 max,int16 min);
void position_pid();
void imu_attitude_algorithm();
void smooth_kalman_filter(IMU_param *imu_data, IMU_param*filtered_data);
void IMU_GetValues();
void stop_car();
void servo_adjustment();
void servo_show();
void servo_arrows_move();
void rotate_servo180();
void rotate_servo270();
void aim_arrows_move();
void aim_show();
void aim_operation();

#endif
